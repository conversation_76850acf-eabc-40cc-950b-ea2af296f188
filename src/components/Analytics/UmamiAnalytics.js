import Script from "next/script";

const UmamiAnalytics = () => {
  if (process.env.NODE_ENV !== "production") {
    return null;
  }

  return (
    <Script
      // src="https://cloud.umami.is/script.js"
      src="https://umami.uniscribe.co/script.js"
      // data-website-id="5ee1518e-d7aa-4f22-8486-c8f345e413ed"
      data-website-id="74b76b68-77b0-4179-bdf7-7e05bc3d824e"
      strategy="afterInteractive"
      defer
    />
  );
};

export default UmamiAnalytics;
