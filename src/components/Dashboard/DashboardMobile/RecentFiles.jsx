"use client";

import { useState, useEffect, useCallback } from "react";
import { transcriptionService } from "@/services/api/transcriptionService";
import { formatDuration, formatCreatedTime } from "@/lib/utils";
import { formatDateWithTimezone } from "@/components/Dashboard/Settings/TimezoneUtils";
import { useTranslations, useLocale } from "next-intl";
import { StatusIcon } from "@/components/Common/StatusIcon";
import { Link } from "@/components/Common/Link";
import { EmptyState } from "../DashboardV2/components/EmptyState";
import { Skeleton } from "@/components/ui/skeleton";

export function RecentFiles() {
  const t = useTranslations("dashboard.mobileDashboard");
  const locale = useLocale();
  const [files, setFiles] = useState([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const fetchFiles = useCallback(async () => {
    if (loading || !hasMore) return;

    try {
      setLoading(true);
      const response = await transcriptionService.getTranscriptionsByPage(
        page,
        10
      );
      const { items, total } = response.data;

      if (page === 1) {
        setFiles(items);
      } else {
        setFiles((prev) => [...prev, ...items]);
      }

      setTotal(total);
      setHasMore(files.length < total);
    } catch (error) {
      console.error("Error fetching files:", error);
    } finally {
      setLoading(false);
    }
  }, [page, loading, hasMore, files.length]);

  useEffect(() => {
    fetchFiles();
  }, [page]);

  const handleScroll = useCallback(
    (e) => {
      if (loading || !hasMore) return;

      const { scrollTop, scrollHeight, clientHeight } = e.target;
      const bottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 1;

      if (bottom && files.length < total) {
        setPage((prev) => prev + 1);
      }
    },
    [loading, hasMore, files.length, total]
  );

  // 骨架屏组件
  const FileSkeleton = () => (
    <div className="bg-white rounded-lg p-3 shadow-sm">
      <div className="flex items-center gap-3">
        <Skeleton className="w-4 h-4 rounded-full" />
        <div className="flex-1">
          <Skeleton className="h-5 w-3/4 mb-2" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
      </div>
    </div>
  );

  // 如果正在加载第一页且没有文件,不显示空状态
  if (!loading && page === 1 && files.length === 0) {
    return (
      <div className="flex-1 overflow-y-auto">
        <EmptyState />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto" onScroll={handleScroll}>
      <h2 className="text-base font-semibold px-4 py-2">{t("recentFiles")}</h2>
      <div className="space-y-2 px-4 pb-20">
        {files.map((file, index) => (
          <Link
            key={`${file.id}-${index}`}
            href={`/transcriptions/${file.id}`}
            className="block bg-white rounded-lg p-3 shadow-sm"
          >
            <div className="flex items-center gap-3">
              <StatusIcon status={file.status} className="w-4 h-4" />
              <div className="flex-1 min-w-0">
                <h3 className="font-medium truncate">{file.filename}</h3>
                <div className="flex items-center text-sm text-gray-500 mt-1">
                  <span>{formatDuration(file.duration) || "-"}</span>
                  <span className="mx-2">•</span>
                  <span>{formatCreatedTime(file.createdTime, locale)}</span>
                </div>
              </div>
            </div>
          </Link>
        ))}
        {loading && (
          <div className="space-y-2">
            <FileSkeleton />
            <FileSkeleton />
            <FileSkeleton />
            <FileSkeleton />
            <FileSkeleton />
            <FileSkeleton />
            <FileSkeleton />
            <FileSkeleton />
          </div>
        )}
      </div>
    </div>
  );
}
