"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DashboardPromotionCard } from "@/components/Dashboard/DashboardV2/components/PromotionCard";
import { useState, useCallback, useRef, useEffect } from "react";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import UploadDialog from "@/components/Dashboard/UploadDialog";
import { YouTubeUploadDialog } from "@/components/Dashboard/YouTubeUpload";
import { transcriptionService } from "@/services/api/transcriptionService";
import { RemainingMinutes } from "./components/RemainingMinutes";
import { FileList } from "./components/FileList";
import { useAuthStore } from "@/stores/useAuthStore";
import supabase from "@/lib/supabaseClient";

import { useRouter } from "@/i18n/navigation";
import LimitReachedDialog from "@/components/Dashboard/LimitReachedDialog";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import AppSumoWelcomeDialog from "@/components/Dashboard/AppSumoWelcomeDialog";
import AppSumoActivationErrorDialog from "@/components/Dashboard/AppSumoActivationErrorDialog";
import { useDashboardInitialize } from "@/hooks/useDashboardInitialize";
import { Upload, Link as LinkIcon, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useUserLimits } from "@/hooks/useUserLimits";
import FreeUserUpgradeDialog from "@/components/Dialog/FreeUserUpgradeDialog";

export default function DashboardV2({ selectedFolderId = "all" }) {
  const t = useTranslations("dashboard");
  const router = useRouter();
  const { fetchEntitlements } = useEntitlementsStore();
  const [showUpload, setShowUpload] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitMessage, setLimitMessage] = useState("");
  const [upgradeSource, setUpgradeSource] = useState("dashboard");
  const { openDialog } = useUpgradeDialogStore();
  const [showAppSumoWelcomeDialog, setShowAppSumoWelcomeDialog] =
    useState(false);
  const [showFreeUserUpgradeDialog, setShowFreeUserUpgradeDialog] =
    useState(false);
  const [upgradeLimitType, setUpgradeLimitType] = useState(null);
  const [isCheckingLimits, setIsCheckingLimits] = useState(false);
  const youtubeDialogRef = useRef(null);
  const { user, appsumoActivationError, clearAppSumoActivationError } =
    useAuthStore();
  const { checkLimits } = useUserLimits();
  const shouldShowPromoCard =
    user && !user.hasActiveSubscription && !user.primaryPlanDetail?.isAppsumo;

  // Define all hooks and callbacks first, before any conditional returns
  const handleUploadSuccess = useCallback(
    async (transcription) => {
      setShowUpload(false);
      try {
        // Create transcription task
        await transcriptionService.doTranscription(transcription.id);
        router.push(`/transcriptions/${transcription.id}`);
      } catch (error) {
        console.error("Failed to create transcription task:", error);
      }
    },
    [router]
  );

  const handleYouTubeSubmitSuccess = useCallback(
    (transcription) => {
      router.push(`/transcriptions/${transcription.id}`);
    },
    [router]
  );

  const onUploadClick = useCallback(async () => {
    // Check if user is free user (using hasPaidPlan field)
    const isFreeUser = user && !user.hasPaidPlan;

    if (isFreeUser) {
      setIsCheckingLimits(true);
      try {
        // Check user limits before showing upload dialog
        const { canProceed, limitType } = await checkLimits();

        if (!canProceed) {
          setUpgradeLimitType(limitType);
          setShowFreeUserUpgradeDialog(true);
          return;
        }
      } finally {
        setIsCheckingLimits(false);
      }
    }

    setShowUpload(true);
    fetchEntitlements();
  }, [fetchEntitlements, checkLimits, user]);

  const onYouTubeClick = useCallback(async () => {
    // Check if user is free user (using hasPaidPlan field)
    const isFreeUser = user && !user.hasPaidPlan;

    if (isFreeUser) {
      setIsCheckingLimits(true);
      try {
        // Check user limits before showing YouTube dialog
        const { canProceed, limitType } = await checkLimits();

        if (!canProceed) {
          setUpgradeLimitType(limitType);
          setShowFreeUserUpgradeDialog(true);
          return;
        }
      } finally {
        setIsCheckingLimits(false);
      }
    }

    youtubeDialogRef.current?.openDialog();
    fetchEntitlements();
  }, [fetchEntitlements, checkLimits, user]);

  useEffect(() => {
    const checkAuth = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session || session?.user?.is_anonymous) {
        router.replace("/auth/signin");
      } else {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [user, router]);

  // 使用共享的 Dashboard 初始化逻辑
  useDashboardInitialize({
    setShowLimitDialog,
    setLimitMessage,
    setUpgradeSource,
    setShowAppSumoWelcomeDialog,
    source: "dashboard",
    isLoading,
  });

  // 处理升级按钮点击
  const handleUpgradeClick = () => {
    setShowLimitDialog(false);
    openDialog({
      source: upgradeSource,
      defaultPlanType: "yearly",
    });
  };

  return (
    <div className="w-full">
      <div className="flex flex-col lg:flex-row gap-4 md:gap-8 h-full min-h-[600px]">
        <div className="flex-1 bg-white rounded-xl flex flex-col">
          <FileList selectedFolderId={selectedFolderId} />
        </div>
        <div className="w-72 space-y-6">
          {/* Quick Actions Section */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-4 relative">
            <h2 className="text-lg font-semibold mb-4">{t("quickActions")}</h2>
            <div className="space-y-3">
              <Button
                onClick={onUploadClick}
                disabled={isCheckingLimits}
                className="w-full bg-custom-bg hover:bg-custom-bg/90 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Upload className="w-4 h-4" />
                {t("uploadAudioVideo")}
              </Button>
              <Button
                onClick={onYouTubeClick}
                disabled={isCheckingLimits}
                className="w-full bg-custom-bg hover:bg-custom-bg/90 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <LinkIcon className="w-4 h-4" />
                {t("pasteYouTubeLink")}
              </Button>
            </div>

            {/* Loading Overlay */}
            {isCheckingLimits && (
              <div className="absolute inset-0 bg-white/80 rounded-lg flex items-center justify-center">
                <Loader2 className="w-5 h-5 animate-spin text-gray-600" />
              </div>
            )}
          </div>

          {/* Usage Summary Section */}
          <div className="bg-white rounded-xl">
            <RemainingMinutes />
          </div>

          {/* Promotion Card Section */}
          {shouldShowPromoCard && (
            <div className="bg-white rounded-xl">
              <DashboardPromotionCard />
            </div>
          )}
        </div>
      </div>

      {/* Upload Dialogs */}
      <UploadDialog
        isOpen={showUpload}
        onOpenChange={setShowUpload}
        onUploadSuccess={handleUploadSuccess}
        selectedFolderId={selectedFolderId}
      />
      <YouTubeUploadDialog
        ref={youtubeDialogRef}
        onTranscribeSubmit={handleYouTubeSubmitSuccess}
        selectedFolderId={selectedFolderId}
      />

      {/* 限制提示弹窗 */}
      <LimitReachedDialog
        isOpen={showLimitDialog}
        onClose={() => setShowLimitDialog(false)}
        onUpgrade={handleUpgradeClick}
        source={upgradeSource}
        message={limitMessage}
      />

      {/* 免费用户升级弹窗 */}
      <FreeUserUpgradeDialog
        isOpen={showFreeUserUpgradeDialog}
        onOpenChange={setShowFreeUserUpgradeDialog}
        limitType={upgradeLimitType}
      />

      {/* AppSumo 欢迎弹窗 */}
      <AppSumoWelcomeDialog
        isOpen={showAppSumoWelcomeDialog}
        onClose={() => setShowAppSumoWelcomeDialog(false)}
        user={user}
      />

      {/* AppSumo 激活错误弹窗 */}
      <AppSumoActivationErrorDialog
        isOpen={!!appsumoActivationError}
        onClose={clearAppSumoActivationError}
        errorCode={appsumoActivationError}
      />
    </div>
  );
}
