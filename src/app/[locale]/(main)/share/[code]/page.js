import { notFound } from "next/navigation";
import SharePageWrapper from "@/components/Share/SharePageWrapper";
import { serverShareService } from "@/services/api/shareService";
import { renderSchemas } from "@/lib/schema";
import {
  generateAlternates,
  generateConsistentMetadata,
  generateStructuredData,
} from "@/lib/metadata";
import { getTranslations } from "next-intl/server";

const cleanShareCode = (code) => {
  try {
    // 先解码 URL 编码的字符串
    const decodedCode = decodeURIComponent(code);
    // 然后分割获取第一部分
    return decodedCode.split("&")[0];
  } catch (error) {
    // 如果解码失败，返回原始 code（处理非编码的正常情况）
    console.error("Error decoding share code:", error);
    return code.split("&")[0];
  }
};

export async function generateMetadata({ params }) {
  const code = cleanShareCode(params.code);
  const locale = params.locale || "en";
  const t = await getTranslations({ locale, namespace: "share" });

  // 从翻译文件获取关键词
  const keywords = t.raw("seoKeywords");

  // 生成随机索引
  const randomIndex = Math.floor(Math.random() * keywords.length);

  // 选择随机关键词
  const randomKeyword = keywords[randomIndex];

  try {
    const transcription =
      await serverShareService.getSharedTranscriptionByCode(code);

    if (!transcription) {
      return {
        title: t("metadata.notFoundTitle"),
        description: t("metadata.notFoundDescription"),
      };
    }

    const defaultDescription = t("metadata.defaultDescription", {
      filename: transcription.filename,
    });
    const summary = transcription.result?.summary || defaultDescription;

    return generateConsistentMetadata(`/share/${code}`, locale, {
      title: t("metadata.title", { filename: transcription.filename }),
      description: defaultDescription,
      ogTitle: t("metadata.ogTitle", { filename: transcription.filename }),
      ogDescription: summary,
      twitterTitle: t("metadata.twitterTitle", {
        filename: transcription.filename,
      }),
      twitterDescription: summary,
      type: "article",
      robots: {
        index: false, // 禁止索引分享页面
        follow: false, // 禁止跟踪链接
        noarchive: true, // 禁止缓存
        nosnippet: true, // 禁止显示摘要
        noimageindex: true, // 禁止索引图片
        googleBot: {
          index: false,
          follow: false,
          noarchive: true,
          nosnippet: true,
          noimageindex: true,
        },
      },
    });
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: t("metadata.errorTitle"),
      description: t("metadata.errorDescription"),
      robots: {
        index: false,
        follow: true,
      },
    };
  }
}

export default async function SharePage({ params }) {
  const code = cleanShareCode(params.code);
  const locale = params.locale || "en";

  // 使用新的 generateStructuredData 函数生成结构化数据
  const schemas = generateStructuredData(`/share/${code}`, locale, {
    title: "Share",
    description: "Share a transcription with your friends and colleagues.",
  });

  const [transcription, planConfig] = await Promise.all([
    serverShareService.getSharedTranscriptionByCode(code),
    serverShareService.getSharerPlanConfig(code),
  ]);

  if (!transcription) {
    return notFound();
  }

  return (
    <>
      {renderSchemas(schemas)}
      <SharePageWrapper
        transcription={transcription}
        planConfig={planConfig}
        initialSegment={null}
        locale={locale}
      />
    </>
  );
}
