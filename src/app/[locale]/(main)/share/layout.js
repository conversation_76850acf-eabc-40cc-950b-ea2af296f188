import { Inter } from "next/font/google";
import "@/app/globals.css";
import Script from "next/script";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  metadataBase: new URL("https://www.uniscribe.co"),
  robots: {
    index: false, // 禁止索引分享页面
    follow: false, // 禁止跟踪链接
    noarchive: true, // 禁止缓存
    nosnippet: true, // 禁止显示摘要
    noimageindex: true, // 禁止索引图片
    googleBot: {
      index: false,
      follow: false,
      noarchive: true,
      nosnippet: true,
      noimageindex: true,
    },
  },
};

export default async function ShareLayout({ children }) {
  return (
    <>
      <Script
        async
        src="https://tally.so/widgets/embed.js"
        strategy="afterInteractive"
      />
      <div className="pt-20">{children}</div>
    </>
  );
}
